// r2Upload.js
const { S3Client, PutObjectCommand } = require('@aws-sdk/client-s3');
const fs = require('fs');

// Cloudflare R2配置
const accountId = '302809262cd0132cd9f3a9a40653c762';
const accessKeyId = '6983beeb15fa54b00d42a2cdd49c8367';
const secretAccessKey = '****************************************************************';
const bucketName = 'file';
const endpoint = `https://${accountId}.r2.cloudflarestorage.com`;

// 创建S3客户端
const s3Client = new S3Client({
  region: 'auto',
  endpoint: endpoint,
  credentials: {
    accessKeyId: accessKeyId,
    secretAccessKey: secretAccessKey,
  },
});

/**
 * 上传文件到Cloudflare R2
 * @param {string} localFilePath - 本地文件路径
 * @param {string} key - R2存储的文件名
 * @returns {Promise<string>} - 返回文件的访问URL
 */
async function uploadToR2(localFilePath, key) {
  try {
    // 读取文件内容
    const fileContent = fs.readFileSync(localFilePath);

    // 创建上传命令
    const command = new PutObjectCommand({
      Bucket: bucketName,
      Key: key,
      Body: fileContent,
      ContentType: 'application/pdf',
    });

    // 执行上传
    const response = await s3Client.send(command);

    // 构建访问URL - 使用自定义域名
    const customDomain = 'https://jianli.zhuxiuxiang.com';
    const url = `${customDomain}/${key}`;
    console.log('R2上传成功:', response);
    return url;

  } catch (error) {
    console.error('R2上传失败:', error);
    throw error;
  }
}

module.exports = uploadToR2;
