<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <title>西牧简历</title>
  <style>
    body {
      font-family: "Segoe UI", "Helvetica Neue", sans-serif;
      background: linear-gradient(135deg, #f0f4ff, #e0ecff);
      margin: 0;
      padding: 0;
      color: #333;
    }

    /* 页面标题样式 */
    .page-title {
      text-align: center;
      padding: 40px 0 30px 0;
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(240, 244, 255, 0.9));
      backdrop-filter: blur(20px);
      margin-bottom: 30px;
      border-bottom: 1px solid rgba(63, 81, 181, 0.1);
      position: relative;
      overflow: hidden;
    }

    .page-title::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
      animation: shimmer 3s infinite;
    }

    @keyframes shimmer {
      0% { left: -100%; }
      100% { left: 100%; }
    }

    .page-title h1 {
      font-size: 36px;
      font-weight: 800;
      margin: 0 0 12px 0;
      text-shadow: 0 4px 8px rgba(63, 81, 181, 0.2);
      letter-spacing: 1px;
      position: relative;
      z-index: 1;
    }

    .page-title h1 .icon {
      font-size: 36px;
      display: inline-block;
      vertical-align: middle;
    }

    .page-title h1 .title-text {
      background: linear-gradient(135deg, #3f51b5, #5c6bc0, #7986cb);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      display: inline-block;
      vertical-align: middle;
    }

    .page-title p {
      font-size: 18px;
      color: #666;
      margin: 0;
      font-weight: 500;
      opacity: 0.9;
      position: relative;
      z-index: 1;
    }



    .container {
      max-width: 680px;
      margin: 0 auto 50px auto;
      background: #fff;
      border-radius: 12px;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
      padding: 50px 60px;
    }

    .header {
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      margin-bottom: 30px;
    }

    .avatar {
      width: 100px;
      height: 100px;
      border-radius: 50%;
      overflow: hidden;
      border: 4px solid #3f51b5;
      position: relative;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .avatar:hover {
      border-color: #5c6bc0;
      transform: scale(1.05);
    }

    .avatar img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .avatar-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      opacity: 0;
      transition: opacity 0.3s ease;
      color: white;
      font-size: 12px;
      text-align: center;
    }

    .avatar:hover .avatar-overlay {
      opacity: 1;
    }

    #avatar-input {
      display: none;
    }

    /* 导出PDF按钮样式 */
    .export-section {
      text-align: center;
      margin: 30px auto 50px auto;
      max-width: 680px;
    }

    .export-btn {
      background: linear-gradient(135deg, #3f51b5, #5c6bc0);
      color: white;
      border: none;
      padding: 12px 30px;
      border-radius: 25px;
      font-size: 16px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      box-shadow: 0 4px 15px rgba(63, 81, 181, 0.3);
      min-width: 150px;
    }

    .export-btn:hover {
      background: linear-gradient(135deg, #5c6bc0, #7986cb);
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(63, 81, 181, 0.4);
    }

    .export-btn:active {
      transform: translateY(0);
      box-shadow: 0 2px 10px rgba(63, 81, 181, 0.3);
    }

    .export-btn:disabled {
      background: #ccc;
      cursor: not-allowed;
      transform: none;
      box-shadow: none;
    }

    /* 作者信息样式 */
    .author-info {
      text-align: center;
      margin: 20px auto 30px auto;
      max-width: 680px;
      padding: 15px 0;
      border-top: 1px solid #e0ecff;
    }

    .author-info p {
      margin: 0;
      font-size: 14px;
      color: #888;
      font-weight: 400;
    }

    /* PDF导出时隐藏网页界面元素 */
    @media print {
      .export-section,
      .page-title,
      .add-btn,
      .author-info {
        display: none !important;
      }
    }

    /* 右键菜单样式 */
    .context-menu {
      position: fixed;
      background: white;
      border: 1px solid #ddd;
      border-radius: 6px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      padding: 4px 0;
      z-index: 1000;
      min-width: 100px;
      display: none;
    }

    .context-menu-item {
      padding: 8px 16px;
      cursor: pointer;
      font-size: 14px;
      color: #333;
      transition: background-color 0.2s;
    }

    .context-menu-item:hover {
      background-color: #f5f5f5;
    }

    .context-menu-item.delete {
      color: #f44336;
    }

    .context-menu-item.delete:hover {
      background-color: #ffebee;
    }



    .title {
      flex: 1;
      margin-left: 30px;
    }

    .title h1, .title p {
      margin: 0;
    }

    .title h1 {
      font-size: 28px;
      color: #3f51b5;
    }

    .title p {
      margin-top: 8px;
      font-size: 16px;
      color: #666;
    }

    h2 {
      color: #303f9f;
      font-size: 20px;
      font-weight: 600;
      margin-top: 40px;
      margin-bottom: 15px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .info {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      font-size: 15px;
      margin-bottom: 30px;
      color: #555;
    }

    .info div {
      margin: 6px 0;
    }

    p, li {
      line-height: 1.6;
      cursor: pointer;
    }



    ul {
      padding-left: 20px;
    }

    .editable{
      margin: 0;
    }

    /* 各模块item间距 */
    #project-list .project-item,
    #work-list .work-item,
    #education-list .education-item {
      margin-bottom: 15px;
    }

    #project-list .project-item:last-child,
    #work-list .work-item:last-child,
    #education-list .education-item:last-child {
      margin-bottom: 0;
    }

    .editable:focus {
      outline: 2px dashed #3f51b5;
      background: #eef2ff;
    }

    button.add-btn {
      font-size: 14px;
      background: #3f51b5;
      color: white;
      border: none;
      padding: 4px 10px;
      border-radius: 5px;
      cursor: pointer;
      transition: background 0.3s;
      flex-shrink: 0;
    }

    button.add-btn:hover {
      background: #5c6bc0;
    }

    @media (max-width: 600px) {
      .page-title {
        padding: 30px 20px 25px 20px;
      }

      .page-title h1 {
        font-size: 28px;
        letter-spacing: 0.5px;
      }

      .page-title p {
        font-size: 16px;
        padding: 0 10px;
      }



      .container {
        padding: 30px 20px;
      }

      .header {
        flex-direction: column;
        align-items: center;
      }

      .title {
        margin-left: 0;
        text-align: center;
        margin-top: 10px;
      }

      .info {
        flex-direction: column;
      }
    }
  </style>
</head>
<body>
  <!-- 页面标题 -->
  <div class="page-title">
    <h1><span class="icon">📋</span> <span class="title-text">西牧简历</span></h1>
    <p>✨ 页面美观 • 操作简单 • 永久免费 ✨</p>
  </div>

  <div class="container">
    <div class="header">
      <div class="avatar" onclick="changeAvatar()">
        <img id="avatar-img" src="https://images.unsplash.com/photo-1603415526960-f7e0328c63b1?crop=faces&fit=crop&w=200&h=200" alt="头像">
        <div class="avatar-overlay">
          点击更换<br>头像
        </div>
      </div>
      <input type="file" id="avatar-input" accept="image/*" onchange="handleAvatarChange(event)">
      <div class="title">
        <h1 class="editable" contenteditable="true">张小新</h1>
        <p class="editable" contenteditable="true">前端工程师 | Vue / React / TypeScript / Node.js</p>
      </div>
    </div>

    <div class="info">
      <div class="editable" contenteditable="true">🧑 男 | 28岁</div>
      <div class="editable" contenteditable="true">📍 上海 | 5 年经验</div>
      <div class="editable" contenteditable="true">📞 13500001234</div>
      <div class="editable" contenteditable="true">🎓 本科学历</div>
    </div>

    <h2>
      <span>教育背景</span>
      <button class="add-btn" onclick="addEducation()"> 添加</button>
    </h2>
    <div id="education-list">
      <div class="education-item">
        <p class="editable" contenteditable="true">
          <strong>上海交通大学</strong>｜计算机科学与技术 本科｜2014 - 2018
        </p>
      </div>
    </div>

    <h2>
      <span>工作经历</span>
      <button class="add-btn" onclick="addWork()"> 添加</button>
    </h2>
    <div id="work-list">
      <div class="work-item">
        <p class="editable" contenteditable="true">
          <strong>字节跳动 | 高级前端工程师</strong> <span style="float:right;">2021.04 - 至今</span><br>
          负责直播平台运营后台开发，优化组件性能、引入自动化测试，提升前端质量。
        </p>
      </div>
      <div class="work-item">
        <p class="editable" contenteditable="true">
          <strong>网易云音乐 | 前端开发工程师</strong> <span style="float:right;">2018.07 - 2021.03</span><br>
          参与多个核心页面重构及组件抽象，推动跨部门前端规范建设。
        </p>
      </div>
    </div>

    <h2>
      <span>项目经验</span>
      <button class="add-btn" onclick="addProject()"> 添加</button>
    </h2>
    <ul id="project-list">
      <li class="project-item">
        <div class="editable" contenteditable="true">
          <strong>智能内容管理平台</strong>（Vue3 + Element Plus）<br>
          支持多角色权限管理、可视化内容编辑器，实现动态组件渲染与数据绑定，服务上百万用户。
        </div>
      </li>
      <li class="project-item">
        <div class="editable" contenteditable="true">
          <strong>跨境电商官网</strong>（React + Next.js）<br>
          实现静态生成与服务端渲染结合，优化 SEO 与首屏性能，移动端适配流畅。
        </div>
      </li>
      <li class="project-item">
        <div class="editable" contenteditable="true">
          <strong>H5 活动页组件平台</strong>（Vite + Vue）<br>
          拖拽式配置系统 + 可复用组件库，支持多种主题模板与导出，节省 80% 页面开发时间。
        </div>
      </li>
    </ul>

    <h2>个人简介</h2>
    <p class="editable" contenteditable="true">
      具备丰富的前端开发经验，精通 Vue / React 等主流框架，擅长构建高性能组件和优雅 UI，参与多个大型项目开发。关注用户体验，热爱探索新技术，具备良好的团队沟通与协作能力。
    </p>
  </div>

  <!-- 导出PDF按钮 -->
  <div class="export-section">
    <button class="export-btn" onclick="exportToPDF()">📄 导出PDF</button>
  </div>

  <!-- 作者信息 -->
  <div class="author-info">
    <p>© 2025 西牧 | 微信：zxx960</p>
  </div>

  <!-- 右键菜单 -->
  <div id="context-menu" class="context-menu">
    <div class="context-menu-item delete" onclick="deleteCurrentItem()">删除</div>
  </div>

  <script>
    // 支持按回车退出编辑状态
    document.querySelectorAll('.editable').forEach(el => {
      el.addEventListener('keydown', (e) => {
        if (e.key === 'Enter' && !e.shiftKey) {
          e.preventDefault();
          el.blur();
        }
      });
    });

    // 更换头像功能
    function changeAvatar() {
      document.getElementById('avatar-input').click();
    }

    function handleAvatarChange(event) {
      const file = event.target.files[0];
      if (file) {
        // 检查文件类型
        if (!file.type.startsWith('image/')) {
          alert('请选择图片文件！');
          return;
        }

        // 检查文件大小（限制为5MB）
        if (file.size > 5 * 1024 * 1024) {
          alert('图片文件不能超过5MB！');
          return;
        }

        const reader = new FileReader();
        reader.onload = function(e) {
          document.getElementById('avatar-img').src = e.target.result;
        };
        reader.readAsDataURL(file);
      }
    }

    // 右键菜单相关变量
    let currentItem = null;
    const contextMenu = document.getElementById('context-menu');

    // 为项目绑定右键菜单事件（通用函数）
    function bindContextMenu(item) {
      item.addEventListener('contextmenu', function(e) {
        e.preventDefault();
        currentItem = item;

        // 显示右键菜单，使用clientX/clientY获取相对于视窗的坐标
        contextMenu.style.display = 'block';
        contextMenu.style.left = e.clientX + 'px';
        contextMenu.style.top = e.clientY + 'px';
      });
    }

    // 删除当前项目（通用删除函数）
    function deleteCurrentItem() {
      if (!currentItem) return;

      let confirmMessage = '确定要删除这个项目吗？';
      if (currentItem.classList.contains('work-item')) {
        confirmMessage = '确定要删除这个工作经历吗？';
      } else if (currentItem.classList.contains('education-item')) {
        confirmMessage = '确定要删除这个教育背景吗？';
      } else if (currentItem.classList.contains('project-item')) {
        confirmMessage = '确定要删除这个项目经验吗？';
      }

      if (confirm(confirmMessage)) {
        currentItem.remove();
        hideContextMenu();
      }
    }

    // 隐藏右键菜单
    function hideContextMenu() {
      contextMenu.style.display = 'none';
      currentItem = null;
    }

    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', function() {
      // 为现有的所有项目绑定右键菜单
      document.querySelectorAll('.work-item, .education-item, .project-item').forEach(bindContextMenu);

      // 点击其他地方隐藏右键菜单
      document.addEventListener('click', hideContextMenu);

      // 阻止右键菜单的点击事件冒泡
      contextMenu.addEventListener('click', function(e) {
        e.stopPropagation();
      });
    });

    // 添加项目经验
    function addProject() {
      const ul = document.getElementById('project-list');
      const li = document.createElement('li');
      li.className = 'project-item';

      const contentDiv = document.createElement('div');
      contentDiv.className = 'editable';
      contentDiv.contentEditable = true;
      contentDiv.innerHTML = '<strong>项目名称</strong>（技术栈）<br>项目描述...';

      li.appendChild(contentDiv);
      ul.appendChild(li);
      contentDiv.focus();

      contentDiv.addEventListener('keydown', (e) => {
        if (e.key === 'Enter' && !e.shiftKey) {
          e.preventDefault();
          contentDiv.blur();
        }
      });

      // 为新添加的项目绑定右键菜单事件
      bindContextMenu(li);
    }

    // 添加工作经历
    function addWork() {
      const div = document.getElementById('work-list');
      const workItem = document.createElement('div');
      workItem.className = 'work-item';

      const p = document.createElement('p');
      p.className = 'editable';
      p.contentEditable = true;
      p.innerHTML = '<strong>公司名称 | 职位</strong> <span style="float:right;">开始时间 - 结束时间</span><br>工作描述...';

      workItem.appendChild(p);
      div.appendChild(workItem);
      p.focus();

      p.addEventListener('keydown', (e) => {
        if (e.key === 'Enter' && !e.shiftKey) {
          e.preventDefault();
          p.blur();
        }
      });

      // 为新添加的工作项目绑定右键菜单事件
      bindContextMenu(workItem);
    }

    // 添加教育背景
    function addEducation() {
      const div = document.getElementById('education-list');
      const educationItem = document.createElement('div');
      educationItem.className = 'education-item';

      const p = document.createElement('p');
      p.className = 'editable';
      p.contentEditable = true;
      p.innerHTML = '<strong>学校名称</strong>｜专业 学历｜开始年份 - 结束年份';

      educationItem.appendChild(p);
      div.appendChild(educationItem);
      p.focus();

      p.addEventListener('keydown', (e) => {
        if (e.key === 'Enter' && !e.shiftKey) {
          e.preventDefault();
          p.blur();
        }
      });

      // 为新添加的教育背景绑定右键菜单事件
      bindContextMenu(educationItem);
    }

    // PDF导出功能
    async function exportToPDF() {
      try {
        // 显示加载状态
        const exportBtn = event.target;
        const originalText = exportBtn.textContent;
        exportBtn.textContent = '导出中...';
        exportBtn.disabled = true;

        // 获取简历内容
        const container = document.querySelector('.container');
        const resumeHTML = container.outerHTML;

        // 获取所有CSS样式
        const styles = Array.from(document.styleSheets)
          .map(styleSheet => {
            try {
              return Array.from(styleSheet.cssRules)
                .map(rule => rule.cssText)
                .join('\n');
            } catch (e) {
              console.warn('无法访问样式表:', e);
              return '';
            }
          })
          .join('\n');

        // 获取内联样式
        const inlineStyles = document.querySelector('style').textContent;
        const allStyles = inlineStyles + '\n' + styles;

        // 发送到服务器生成PDF
        const response = await fetch('/api/export-pdf', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            html: resumeHTML,
            css: allStyles
          })
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        // 下载PDF文件
        const blob = await response.blob();
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = '简历.pdf';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        console.log('PDF导出成功');

      } catch (error) {
        console.error('PDF导出失败:', error);
        alert('PDF导出失败，请稍后重试');
      } finally {
        // 恢复按钮状态
        const exportBtn = document.querySelector('.export-btn');
        if (exportBtn) {
          exportBtn.textContent = '📄 导出PDF';
          exportBtn.disabled = false;
        }
      }
    }
  </script>
</body>
</html>
