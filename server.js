const express = require('express');
const axios = require('axios');
const path = require('path');
const fs = require('fs');
const extractStructuredData = require('./aiExtract');
const uploadToR2 = require('./r2Upload');
const { connectAndInit, saveResumeToDatabase } = require('./db');
const app = express();
const PORT = process.env.PORT || 3000;

// 中间件
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 静态文件服务
app.use(express.static(path.join(__dirname, 'public')));

// 主页路由
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// PDF导出API
app.post('/api/export-pdf', async (req, res) => {
  try {
    console.log('开始生成PDF...');

    // 获取请求数据
    const { html, css } = req.body;

    if (!html) {
      return res.status(400).json({ error: '缺少HTML内容' });
    }

    // 构建完整的HTML文档
    const fullHtml = `
      <!DOCTYPE html>
      <html lang="zh-CN">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>简历</title>
        <style>
          ${css || ''}

          /* PDF专用样式 */
          body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background: white !important;
            color: #333;
            box-sizing: border-box;
          }

          * {
            box-sizing: border-box !important;
            -webkit-print-color-adjust: exact !important;
            color-adjust: exact !important;
          }

          .container {
            box-shadow: none !important;
            margin: 0 auto !important;
            border-radius: 0 !important;
            max-width: none !important;
            width: 100% !important;
            padding: 25px 35px !important;
            box-sizing: border-box !important;
          }

          /* 隐藏网页界面元素 */
          .context-menu,
          .export-section,
          .page-title,
          .add-btn,
          .author-info {
            display: none !important;
          }
        </style>
      </head>
      <body>
        ${html}
      </body>
      </html>
    `;

    // 使用 Browserless 服务生成 PDF
    const browserlessBaseUrl = 'https://zxx-browserless.zeabur.app';
    const token = 'ON72V618eIYa9B0d5tsF34hTnxEJyGWH';
    const browserlessPdfUrl = `${browserlessBaseUrl}/pdf?token=${token}`;

    const response = await axios.post(browserlessPdfUrl, {
      html: fullHtml,
      options: {
        format: 'A4',
        printBackground: true,
        margin: {
          top: '8mm',
          right: '8mm',
          bottom: '8mm',
          left: '8mm'
        },
      }
    }, {
      headers: {
        'Content-Type': 'application/json',
      },
      responseType: 'arraybuffer'
    });

    // 获取 PDF 数据
    const pdf = Buffer.from(response.data);

    console.log('PDF生成成功');

    // 调用aiExtract.js中的函数解析HTML内容
    try {
      console.log('开始AI解析简历内容...');

      // 将HTML转换为纯文本（简单的HTML标签移除）
      const textContent = html
        .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '') // 移除script标签
        .replace(/<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/gi, '') // 移除style标签
        .replace(/<[^>]*>/g, ' ') // 移除所有HTML标签
        .replace(/\s+/g, ' ') // 合并多个空格
        .trim();

      const parsedData = await extractStructuredData(textContent);
      console.log('AI解析完成，提取的信息:', JSON.stringify(parsedData, null, 2));

      // AI解析完成后，上传PDF到Cloudflare R2
      try {
        console.log('开始上传PDF到Cloudflare R2...');

        // 生成临时文件名
        const timestamp = Date.now();
        const tempFileName = `temp_resume_${timestamp}.pdf`;
        const tempFilePath = path.join(__dirname, tempFileName);

        // 将PDF保存到临时文件
        fs.writeFileSync(tempFilePath, pdf);

        // 生成R2存储的文件键名
        const r2Key = `resumes/${timestamp}_${parsedData.姓名 || 'unknown'}.pdf`;

        // 上传到R2
        const fileUrl = await uploadToR2(tempFilePath, r2Key);
        console.log('PDF上传成功，访问URL:', fileUrl);

        // 删除临时文件
        fs.unlinkSync(tempFilePath);
        console.log('临时文件已删除');

        // 将解析结果和文件URL保存到数据库
        try {
          const filename = `${timestamp}_${parsedData.姓名 || 'unknown'}.pdf`;
          const dbId = await saveResumeToDatabase(filename, parsedData, fileUrl);
          console.log('数据保存成功，数据库ID:', dbId);
        } catch (dbError) {
          console.error('数据库保存失败:', dbError.message);
          // 数据库保存失败不影响PDF导出，只记录错误
        }

      } catch (uploadError) {
        console.error('PDF上传失败:', uploadError.message);
        // 上传失败不影响PDF导出，只记录错误
      }

    } catch (aiError) {
      console.error('AI解析过程中出错:', aiError.message);
      // AI解析失败不影响PDF导出，只记录错误
    }

    // 设置响应头
    res.set({
      'Content-Type': 'application/pdf',
      'Content-Disposition': 'attachment; filename="resume.pdf"',
      'Content-Length': pdf.length
    });
    
    // 发送PDF
    res.send(pdf);

  } catch (error) {
    console.error('PDF生成失败:', error);
    res.status(500).json({
      error: 'PDF生成失败',
      message: error.message
    });
  }
});

// 健康检查接口
app.get('/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

// 错误处理中间件
app.use((error, req, res, next) => {
  console.error('服务器错误:', error);
  res.status(500).json({ error: '服务器内部错误' });
});

// 404处理
app.use((req, res) => {
  res.status(404).json({ error: '页面未找到' });
});

// 启动服务器
app.listen(PORT, async () => {
  console.log(`🚀 服务器运行在 http://localhost:${PORT}`);
  console.log(`📄 简历页面: http://localhost:${PORT}`);
  console.log(`🔧 PDF导出API: POST http://localhost:${PORT}/api/export-pdf`);
  console.log(`⚡ 开发模式: 文件变化时自动重启`);

  // 初始化数据库连接
  await connectAndInit();
});

// 优雅关闭
process.on('SIGTERM', () => {
  console.log('收到SIGTERM信号，正在关闭服务器...');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('收到SIGINT信号，正在关闭服务器...');
  process.exit(0);
});
