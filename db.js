// db.js
const { Pool } = require('pg');

const db = new Pool({
  connectionString: '**********************************************************************/zeabur',
  ssl: false
});

// 初始化数据库连接和表结构
async function connectAndInit() {
  try {
    await db.query('SELECT NOW()');
    console.log('✅ 已连接 PostgreSQL 数据库');
    await initializeDatabase();
  } catch (err) {
    console.error('❌ 数据库连接失败:', err.message);
  }
}

// 创建简历数据表
async function initializeDatabase() {
  const createTableSQL = `
    CREATE TABLE IF NOT EXISTS resumesgenerate (
      id SERIAL PRIMARY KEY,
      filename VARCHAR(255) NOT NULL,
      file_url TEXT,
      name VARCHAR(100),
      gender VARCHAR(10),
      age VARCHAR(20),
      work_experience VARCHAR(50),
      phone VARCHAR(20) UNIQUE,
      education VARCHAR(50),
      email VARCHAR(100),
      position VARCHAR(100),
      expected_city VARCHAR(50),
      created_at TIMESTAMP DEFAULT NOW(),
      updated_at TIMESTAMP DEFAULT NOW()
    )
  `;
  await db.query(createTableSQL);

  // 如果表已存在但phone字段没有唯一约束，添加约束
  try {
    await db.query(`
      ALTER TABLE resumesgenerate
      ADD CONSTRAINT resumesgenerate_phone_unique UNIQUE (phone)
    `);
    console.log('📞 手机号唯一约束已添加');
  } catch (error) {
    // 约束可能已存在，忽略错误
    if (!error.message.includes('already exists')) {
      console.log('📞 手机号唯一约束已存在');
    }
  }

  console.log('📄 简历表已初始化');
}

// 插入简历数据（如果手机号已存在则覆盖）
async function saveResumeToDatabase(filename, resumeData, fileUrl = null) {
  const upsertSQL = `
    INSERT INTO resumesgenerate (
      filename, file_url, name, gender, age, work_experience,
      phone, education, email, position, expected_city
    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
    ON CONFLICT (phone)
    DO UPDATE SET
      filename = EXCLUDED.filename,
      file_url = EXCLUDED.file_url,
      name = EXCLUDED.name,
      gender = EXCLUDED.gender,
      age = EXCLUDED.age,
      work_experience = EXCLUDED.work_experience,
      education = EXCLUDED.education,
      email = EXCLUDED.email,
      position = EXCLUDED.position,
      expected_city = EXCLUDED.expected_city,
      updated_at = NOW()
    RETURNING id
  `;
  const values = [
    filename,
    fileUrl,
    resumeData.姓名 || null,
    resumeData.性别 || null,
    resumeData.年龄 || null,
    resumeData.工作年限 || null,
    resumeData.手机号 || null,
    resumeData.学历 || null,
    resumeData.邮箱 || null,
    resumeData.求职岗位 || null,
    resumeData.期望城市 || null
  ];
  const result = await db.query(upsertSQL, values);
  return result.rows[0].id;
}

// 分页查询简历
async function getResumes(page = 1, limit = 10) {
  const offset = (page - 1) * limit;

  const countResult = await db.query('SELECT COUNT(*) as total FROM resumesgenerate');
  const total = parseInt(countResult.rows[0].total);

  const selectSQL = `
    SELECT id, filename, file_url, name, gender, age, work_experience,
           phone, education, email, position, expected_city,
           created_at, updated_at
    FROM resumesgenerate
    ORDER BY created_at DESC
    LIMIT $1 OFFSET $2
  `;
  const dataResult = await db.query(selectSQL, [limit, offset]);

  return {
    data: dataResult.rows,
    pagination: {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit)
    }
  };
}

// 根据 ID 获取单个简历
async function getResumeById(id) {
  const selectSQL = `
    SELECT id, filename, file_url, name, gender, age, work_experience,
           phone, education, email, position, expected_city,
           created_at, updated_at
    FROM resumesgenerate
    WHERE id = $1
  `;
  const result = await db.query(selectSQL, [id]);
  return result.rows[0] || null;
}

// 关闭数据库连接（用于退出时清理）
async function closeDatabase() {
  await db.end();
  console.log('🔌 数据库连接已关闭');
}

module.exports = {
  connectAndInit,
  saveResumeToDatabase,
  getResumes,
  getResumeById,
  closeDatabase
};
