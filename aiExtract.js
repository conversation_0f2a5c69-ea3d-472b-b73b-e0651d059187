const axios = require('axios');

// 直接写入 API 密钥（用于 MiniMax 文本对话接口）
const API_KEY = '************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************';

/**
 * 使用 MiniMax AI 提取简历中的结构化信息
 * @param {string} resumeText - PDF提取出的简历文本内容
 * @returns {Promise<Object>} - 提取出的结构化简历信息
 */
async function extractStructuredData(resumeText) {
  const prompt = `你是一个简历信息提取器。你的任务是从提供的简历文本中准确识别并提取候选人的关键信息。
请仅以JSON对象的形式返回这些信息，格式如下：{"姓名": "xxx", "性别": "xxx", "年龄": "xxx", "工作年限": "xxx", "手机号": "xxx", "学历": "xxx", "邮箱": "xxx", "求职岗位": "xxx", "期望城市": "xxx"}。
如果某个信息无法找到，请用"未找到"填充。注意：学历指最高学历（如本科、硕士、博士等），求职岗位指应聘的职位或期望岗位，
工作年限指工作经验的总年数，期望城市指求职者期望工作的城市或地区，有些人会写成居住地，也归为期望城市，简历文本：${resumeText}`;

  try {
    const response = await axios.post('https://api.minimaxi.com/v1/text/chatcompletion_v2', {
      model: 'MiniMax-Text-01',
      messages: [
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature: 0.1,
      max_tokens: 1000
    }, {
      headers: {
        'Authorization': `Bearer ${API_KEY}`,
        'Content-Type': 'application/json'
      },
      timeout: 30000
    });

    if (!response.data || !response.data.choices || response.data.choices.length === 0) {
      throw new Error('AI API返回数据格式错误');
    }

    const content = response.data.choices[0].message.content;

    const jsonMatch = content.match(/\{[\s\S]*\}/);
    if (jsonMatch) {
      return JSON.parse(jsonMatch[0]);
    } else {
      throw new Error('AI返回内容中未找到有效的JSON格式');
    }

  } catch (error) {
    console.error('AI提取失败:', error);
    if (error.response) {
      console.error('API错误响应:', error.response.data);
      throw new Error(`AI API调用失败: ${error.response.data.error || error.message}`);
    }
    throw new Error(`AI提取失败: ${error.message}`);
  }
}

module.exports = extractStructuredData;
