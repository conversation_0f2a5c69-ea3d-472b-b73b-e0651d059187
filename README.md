# 简历PDF导出服务

基于Express + Browserless服务的简历PDF导出服务，支持在线编辑简历并导出为PDF格式。

## 功能特点

- 📝 **在线编辑**: 支持直接在网页上编辑简历内容
- 🖼️ **头像上传**: 支持从本地选择头像图片
- ➕ **动态添加**: 支持添加教育背景、工作经历、项目经验
- 🗑️ **右键删除**: 支持右键删除不需要的条目
- 📄 **PDF导出**: 一键导出高质量PDF简历
- 🎨 **样式保真**: 完美保持网页样式和布局

## 项目结构

```
├── server.js          # Express服务器
├── package.json       # 项目依赖
├── README.md          # 说明文档
└── public/
    └── index.html     # 简历页面
```

## 安装和运行

### 1. 安装依赖

```bash
npm install
```

### 2. 启动服务

```bash
# 生产环境
npm start

# 开发环境（自动重启）
npm run dev
# 或者直接运行
npx nodemon server.js
# 或者使用批处理脚本（Windows）
dev.bat
```

#### 开发环境特性
- **自动重启**: 文件变化时自动重启服务器
- **监听文件**: 监听 `server.js` 和 `public/` 目录
- **支持格式**: `.js`, `.html`, `.css`, `.json` 文件
- **手动重启**: 在终端输入 `rs` 然后按回车
- **延迟重启**: 1秒延迟，避免频繁重启

### 3. 访问应用

打开浏览器访问: http://localhost:3000

## API接口

### PDF导出接口

**POST** `/api/export-pdf`

**请求体:**
```json
{
  "html": "简历HTML内容",
  "css": "CSS样式"
}
```

**响应:**
- 成功: 返回PDF文件流
- 失败: 返回错误信息JSON

### 健康检查接口

**GET** `/health`

**响应:**
```json
{
  "status": "ok",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

## 使用说明

1. **编辑简历**: 点击任意文本区域进行编辑
2. **更换头像**: 点击头像区域选择本地图片
3. **添加内容**: 点击各部分的"添加"按钮
4. **删除内容**: 右键点击要删除的条目
5. **导出PDF**: 点击"导出PDF"按钮

## 部署说明

### Docker部署

创建Dockerfile:
```dockerfile
FROM node:18-alpine

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .

EXPOSE 3000
CMD ["npm", "start"]
```

### 环境变量

- `PORT`: 服务端口 (默认: 3000)
- `NODE_ENV`: 运行环境 (development/production)
- `BROWSERLESS_URL`: Browserless服务地址 (可选，默认使用内置配置)
- `BROWSERLESS_TOKEN`: Browserless服务令牌 (可选，默认使用内置配置)

## 技术栈

- **后端**: Node.js + Express
- **PDF生成**: Browserless云服务
- **前端**: 原生HTML/CSS/JavaScript

## 注意事项

1. **网络连接**: 需要稳定的网络连接访问Browserless服务
2. **字体支持**: Browserless服务已内置中文字体支持
3. **并发限制**: Browserless服务支持高并发PDF生成
4. **文件大小**: 上传的头像图片限制为5MB

## 故障排除

### 常见问题

1. **PDF生成失败**
   - 检查网络连接是否正常
   - 确认Browserless服务可访问

2. **中文字体显示异常**
   - Browserless服务已内置中文字体
   - 检查CSS字体设置

3. **头像上传失败**
   - 检查文件大小是否超过5MB
   - 确认文件格式为图片类型

## 许可证

MIT License
